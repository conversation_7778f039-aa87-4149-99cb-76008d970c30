-- 设备二维码表
CREATE TABLE IF NOT EXISTS `fzh_machine_qrcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `machine_id` int(11) NOT NULL COMMENT '设备ID',
  `filename` varchar(255) NOT NULL COMMENT '二维码文件名',
  `qrcode_url` varchar(500) NOT NULL COMMENT '二维码访问URL',
  `scene` varchar(100) NOT NULL COMMENT '场景值',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `machine_id` (`machine_id`),
  KEY `scene` (`scene`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备二维码表';
