<?php

namespace page;

use think\Paginator;

class Page extends Paginator
{
    protected $url;

    //首页
    protected function home()
    {
        if ($this->currentPage() > 1) {
            return "<a href='" . $this->url(1) . "' title='首页' class='pjax-link'>首页</a>";
        } else {
            return "<p class='disabled'>首页</p>";
        }
    }

    //上一页
    protected function prev()
    {
        if ($this->currentPage() > 1) {
            return "<a href='" . $this->url($this->currentPage - 1) . "' title='上一页' class='pjax-link'>上一页</a>";
        } else {
            return "<p class='disabled'>上一页</p>";
        }
    }

    //下一页
    protected function next()
    {
        if ($this->hasMore) {
            return "<a href='" . $this->url($this->currentPage + 1) . "' title='下一页' class='pjax-link'>下一页</a>";
        } else {
            return "<p class='disabled'>下一页</p>";
        }
    }

    //尾页
    protected function last()
    {
        if ($this->hasMore) {
            return "<a href='" . $this->url($this->lastPage) . "' title='尾页' class='pjax-link'>尾页</a>";
        } else {
            return "<p class='disabled'>尾页</p>";
        }
    }

    //统计信息
    protected function info()
    {
        $html = '<div class="pageRemark">';
        $html .= '总共有<b>' . $this->lastPage . '</b>页，';
        $html .= '<b>' . $this->total . '</b>条数据&nbsp;&nbsp;';
        $html .= '<select id="pagesize" onchange="changePageSize(this.value)"> ';
        $html .= '<option value="10" ' . ($this->listRows == 10 ? 'selected' : '') . '>10 条/页</option> ';
        $html .= '<option value="20" ' . ($this->listRows == 20 ? 'selected' : '') . '>20 条/页</option> ';
        $html .= '<option value="30" ' . ($this->listRows == 30 ? 'selected' : '') . '>30 条/页</option> ';
        $html .= '<option value="50" ' . ($this->listRows == 50 ? 'selected' : '') . '>50 条/页</option> ';
        $html .= '<option value="100" ' . ($this->listRows == 100 ? 'selected' : '') . '>100 条/页</option> ';
        $html .= '</select> ';
        $html .= '&nbsp;&nbsp;到第<input id="pageInput" type="text" min="1" value="' . $this->currentPage . '" class="page-input">页 ';
        $html .= '<button type="button" onclick="gotoPage()" class="page-btn">确定</button> ';
        $html .= '</div>';
        return $html;
    }

    /**
     * 页码按钮
     * @return string
     */
    protected function getLinks()
    {

        $block = [
            'first'  => null,
            'slider' => null,
            'last'   => null
        ];

        $side   = 3;
        $window = $side * 2;

        if ($this->lastPage < $window + 6) {
            $block['first'] = $this->getUrlRange(1, $this->lastPage);
        } elseif ($this->currentPage <= $window) {
            $block['first'] = $this->getUrlRange(1, $window + 2);
            $block['last']  = $this->getUrlRange($this->lastPage - 1, $this->lastPage);
        } elseif ($this->currentPage > ($this->lastPage - $window)) {
            $block['first'] = $this->getUrlRange(1, 2);
            $block['last']  = $this->getUrlRange($this->lastPage - ($window + 2), $this->lastPage);
        } else {
            $block['first']  = $this->getUrlRange(1, 2);
            $block['slider'] = $this->getUrlRange($this->currentPage - $side, $this->currentPage + $side);
            $block['last']   = $this->getUrlRange($this->lastPage - 1, $this->lastPage);
        }

        $html = '';

        if (is_array($block['first'])) {
            $html .= $this->getUrlLinks($block['first']);
        }

        if (is_array($block['slider'])) {
            $html .= $this->getDots();
            $html .= $this->getUrlLinks($block['slider']);
        }

        if (is_array($block['last'])) {
            $html .= $this->getDots();
            $html .= $this->getUrlLinks($block['last']);
        }

        return $html;
    }

    /**
     * 渲染分页html
     * @return mixed
     */
    public function render()
    {
        // 只有页数大于1时才显示分页
        if ($this->lastPage > 1) {
            if ($this->simple) {
                return sprintf(
                    '%s<div class="pagination">%s %s %s</div>',
                    $this->css(),
                    $this->prev(),
                    $this->getLinks(),
                    $this->next()
                );
            } else {
                return sprintf(
                    '%s<div class="pagination">%s %s %s %s %s %s</div>%s',
                    $this->css(),
                    $this->home(),
                    $this->prev(),
                    $this->getLinks(),
                    $this->next(),
                    $this->last(),
                    $this->info(),
                    $this->js()
                );
            }
        }
        // 页数小于等于1时返回空字符串，不显示分页
        return '';
    }

    /**
     * 生成一个可点击的按钮
     *
     * @param  string $url
     * @param  int    $page
     * @return string
     */
    protected function getAvailablePageWrapper($url, $page)
    {
        return '<a href="' . htmlentities($url) . '" class="pjax-link">' . $page . '</a>';
    }

    /**
     * 生成一个禁用的按钮
     *
     * @param  string $text
     * @return string
     */
    protected function getDisabledTextWrapper($text)
    {
        return '<p class="pageEllipsis">' . $text . '</p>';
    }

    /**
     * 生成一个激活的按钮
     *
     * @param  string $text
     * @return string
     */
    protected function getActivePageWrapper($text)
    {
        return '<a href="" class="cur">' . $text . '</a>';
    }

    /**
     * 生成省略号按钮
     *
     * @return string
     */
    protected function getDots()
    {
        return $this->getDisabledTextWrapper('...');
    }

    /**
     * 批量生成页码按钮.
     *
     * @param  array $urls
     * @return string
     */
    protected function getUrlLinks(array $urls)
    {
        $html = '';

        foreach ($urls as $page => $url) {
            $html .= $this->getPageLinkWrapper($url, $page);
        }

        return $html;
    }

    /**
     * 生成普通页码按钮
     *
     * @param  string $url
     * @param  int    $page
     * @return string
     */
    protected function getPageLinkWrapper($url, $page)
    {
        if ($page == $this->currentPage()) {
            return $this->getActivePageWrapper($page);
        }

        return $this->getAvailablePageWrapper($url, $page);
    }
    protected function js()
    {
        return '<script type="text/javascript">
		var page=document.getElementById("pagesize"); 
		page.addEventListener("change", function() {
		var _index = this.selectedIndex;
        var size = this.options[_index].value;
		var url="' . $this->url(1) . '";	
		if(url.indexOf("&size") !=-1) {
			var index=url.lastIndexOf("&size");
			url=url.substring(0,index);
		}
		url+="&size="+size;
		window.location.href=url});
		function gotoPage(){
			var input=document.getElementById("pageInput");
			var url="' . $this->url(1) . '";
			if(url.indexOf("&page") !=-1) {
				var index=url.lastIndexOf("&page");
				url=url.substring(0,index);
			}
			url+="&page="+input.value;
			window.location.href=url
		}
		</script>';
    }

    /**
     * 分页样式
     */
    protected function css()
    {
        return '<style>
            .pagination {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                gap: 8px;
                padding: 8px 0;
                margin: 15px 0;
                justify-content: center;
            }
            .pagination a, .pagination p {
                display: inline-block;
                padding: 6px 12px;
                margin: 0 2px;
                border-radius: 3px;
                text-decoration: none;
                color: #333;
                background: #f5f5f5;
                border: 1px solid #ddd;
                transition: all 0.3s ease;
                font-size: 12px;
            }
            .pagination a:hover {
                background: #e0e0e0;
                border-color: #ccc;
            }
            .pagination .cur {
                background: #1890ff;
                border-color: #1890ff;
                color: white;
            }
            .pagination .disabled {
                color: #ccc;
                background: #f5f5f5;
                border-color: #ddd;
                cursor: not-allowed;
            }
            .pagination .pageRemark {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #666;
                font-size: 12px;
            }
            .pagination .pageRemark b {
                color: #333;
            }
            .pagination #pagesize {
                padding: 4px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background: white;
                font-size: 12px;
            }
            .pagination .page-input {
                width: 50px;
                padding: 4px;
                border: 1px solid #ddd;
                border-radius: 3px;
                text-align: center;
                font-size: 12px;
            }
            .pagination .page-btn {
                padding: 4px 10px;
                background: #1890ff;
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
                transition: background 0.3s ease;
                font-size: 12px;
            }
            .pagination .page-btn:hover {
                background: #40a9ff;
            }
        </style>';
    }
}
