<?php

/**
 * @file            AjaxController.class.php
 * @version         1.0
 * @date            Fri, 23 Feb 2018 11:12:07 GMT
 * @description     This is the controller class for data "ajax"
 */

namespace app\admin\controller;

use think\Controller;
use think\Model;
use think\Db;
use think\facade\Request;


class Ajax extends Controller
{
    // 获取二级菜单
    public function get_child_menus(){
        $authRuleModel = db('AuthRule');
        $pid = input('post.group_id');

        $ajax_data = $authRuleModel->field('id, title')->where("pid = $pid")->order("sort")->select();

        return json($ajax_data);
    }

    // 获取二级菜单
    public function get_child_menus_org(){
        $authRuleModel = db('OrgAuthRule');
        $pid = input('post.group_id');

        $ajax_data = $authRuleModel->field('id, title')->where("pid = $pid")->order("sort")->select();

        return json($ajax_data);
    }

    
     /**
     * 获取省份下城市列表
     */
    public function get_citys(){
        $province_id = input('province_id','');
        $res = get_area('2', $province_id);
        $citys = array();
        foreach($res as $key => $row){
            $citys[$row['id']] = $row['area_name'];
        }
        exit(json_encode($citys));
    }

     /**
     * 获取城市下区县列表
     */
    public function get_districts(){
        $city_id = input('city_id','');
        $res = get_region('3', $city_id, '');
        $areas = array();
        foreach($res as $key => $row){
            $areas[$row['id']] = $row['area_name'];
        }
        exit(json_encode($areas));
    }

    /**
     * 批量处理状态
     */
    public function product_status() {
        $params = input('post.');
        $ids = $params['product_id'];
        $status = $params['product_status'];
        if (isset($params['product_id']) && !empty($params['product_id'])) {
            if (count($ids) < 1) {
                $this->error('商品传入数量不足');
            }
            //开启事务来存储数据
            Db::startTrans();
            try {
                //状态
                foreach ($ids as $v) {
                    $data = array(
                        'status' => $status,
                        'updated_at' => time()
                    );
                    db('product')->where("product_id",$v)->update($data);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('批量处理失败:' . $e->getMessage());
            }
            $this->success('批量处理成功', url('/Product/index'));
        } else {
            $this->error('未知的状态');
        }
    }

}
