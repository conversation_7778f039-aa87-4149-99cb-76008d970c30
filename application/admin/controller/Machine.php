<?php
/**
 * @file            Machine.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     设备管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\service\WxService;


class Machine extends Base{

    public function index(){
        
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');

        $where = [];

        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.consignee', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.list_rows');
        $rows = db('machine')->alias('t1')
            ->leftJoin("factory f","t1.factory_id = f.id")
            ->field("t1.*, f.name factory_name")
            ->where($where)
            ->order('t1.id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('machine')->where('id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);

            $data['id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            $data['code'] = input('code', '');
            $data['model'] = input('model', '');
            $data['mac'] = input('mac', '');
            $data['factory_id'] = input('factory_id', 0);
            $data['desc'] = input('desc', '');
            $data['status'] = input('status', 1);

            if ($create) {
                if (!db('machine')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['code', '=', $data['name']],
                    ['id', "<>", $params['id']]
                ];
                $exist = db('machine')->where($map)->count();
                if ($exist > 0) {
                    $this->error('设备编码已存在');
                }
                if (db('machine')->update($data) === false) {
                    $this->error('设备更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            return $this->fetch();
        }
    }

    /**
     * 处理请求
     */
    public function qrcode()
    {
        if (!Request::isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $action = input('action', '');
        $machine_id = input('machine_id', 0);
        $machine_ids = input('machine_ids/a', []);

        switch ($action) {
            case 'generate_qrcode':
                if (empty($machine_id)) {
                    return json(['code' => 0, 'msg' => '设备ID不能为空']);
                }
                return $this->generateMachineQRCode($machine_id);

            case 'batch_generate_qrcode':
                if (empty($machine_ids)) {
                    return json(['code' => 0, 'msg' => '请选择要生成二维码的设备']);
                }
                return $this->batchGenerateQRCode($machine_ids);

            case 'get_qrcode':
                if (empty($machine_id)) {
                    return json(['code' => 0, 'msg' => '设备ID不能为空']);
                }
                return $this->getMachineQRCode($machine_id);

            default:
                return json(['code' => 0, 'msg' => '未知操作']);
        }
    }

    /**
     * 生成单个设备二维码
     */
    private function generateMachineQRCode($machine_id)
    {
        try {
            // 获取设备信息
            $machine = db('machine')->where('id', $machine_id)->find();
            if (!$machine) {
                return json(['code' => 0, 'msg' => '设备不存在']);
            }

            $wxService = new WxService();
            $scene = 'machine_' . $machine_id;

            $result = $wxService->getUnlimitedQRCode($scene);

            // 保存二维码信息到数据库
            $this->saveMachineQRCode($machine_id, $result['filename'], $result['url'], $scene);

            return json([
                'code' => 1,
                'msg' => '二维码生成成功',
                'data' => [
                    'machine_code' => $machine['code'],
                    'qrcode_url' => $result['url'],
                    'filename' => $result['filename']
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '生成二维码失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 批量生成设备二维码
     */
    private function batchGenerateQRCode($machine_ids)
    {
        try {
            // 获取设备信息
            $machines = db('machine')->where('id', 'in', $machine_ids)->select();
            if (empty($machines)) {
                return json(['code' => 0, 'msg' => '未找到设备']);
            }

            $wxService = new WxService();
            $results = $wxService->generateMachineQRCodes($machines);

            $success_count = 0;
            $fail_count = 0;

            foreach ($results as $result) {
                if ($result['success']) {
                    $success_count++;
                    // 保存二维码信息到数据库
                    $scene = 'machine_' . $result['machine_id'];
                    $this->saveMachineQRCode($result['machine_id'], $result['filename'], $result['url'], $scene);
                } else {
                    $fail_count++;
                }
            }

            $msg = "批量生成完成：成功 {$success_count} 个";
            if ($fail_count > 0) {
                $msg .= "，失败 {$fail_count} 个";
            }

            return json(['code' => 1, 'msg' => $msg, 'data' => $results]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量生成二维码失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取设备二维码信息
     */
    private function getMachineQRCode($machine_id)
    {
        try {
            // 获取设备信息
            $machine = db('machine')->where('id', $machine_id)->find();
            if (!$machine) {
                return json(['code' => 0, 'msg' => '设备不存在']);
            }

            // 获取二维码信息
            $qrcode = db('machine_qrcode')->where('machine_id', $machine_id)->find();
            if (!$qrcode) {
                return json(['code' => 0, 'msg' => '该设备还未生成二维码']);
            }

            // 检查二维码文件是否存在
            // $file_path = env('root_path') . 'public' . str_replace('/static', '/static', $qrcode['qrcode_url']);
            // if (!file_exists($file_path)) {
            //     return json(['code' => 0, 'msg' => '二维码文件不存在，请重新生成']);
            // }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'machine_code' => $machine['code'],
                    'qrcode_url' => $qrcode['qrcode_url'],
                    'filename' => $qrcode['filename'],
                    'scene' => $qrcode['scene'],
                    'create_time' => date('Y-m-d H:i:s', $qrcode['create_time'])
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取二维码失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 保存设备二维码信息到数据库
     */
    private function saveMachineQRCode($machine_id, $filename, $url, $scene)
    {
        $time = time();

        // 检查是否已存在二维码记录
        $exist = db('machine_qrcode')->where('machine_id', $machine_id)->find();

        $data = [
            'machine_id' => $machine_id,
            'filename' => $filename,
            'qrcode_url' => $url,
            'scene' => $scene,
            'update_time' => $time
        ];

        if ($exist) {
            // 更新记录
            db('machine_qrcode')->where('id', $exist['id'])->update($data);
        } else {
            // 新增记录
            $data['create_time'] = $time;
            db('machine_qrcode')->insert($data);
        }
    }
}

?>