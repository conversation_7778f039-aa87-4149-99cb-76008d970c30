<?php
/**
 * @file            Machine.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     设备管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;


class Machine extends Base{

    public function index(){
        
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');

        $where = [];

        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.consignee', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.list_rows');
        $rows = db('machine')->alias('t1')
            ->leftJoin("factory f","t1.factory_id = f.id")
            ->field("t1.*, f.name factory_name")
            ->where($where)
            ->order('t1.id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('machine')->where('id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);

            $data['id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            $data['code'] = input('code', '');
            $data['model'] = input('model', '');
            $data['mac'] = input('mac', '');
            $data['factory_id'] = input('factory_id', 0);
            $data['desc'] = input('desc', '');
            $data['status'] = input('status', 1);

            if ($create) {
                if (!db('machine')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['code', '=', $data['name']],
                    ['id', "<>", $params['id']]
                ];
                $exist = db('machine')->where($map)->count();
                if ($exist > 0) {
                    $this->error('设备编码已存在');
                }
                if (db('machine')->update($data) === false) {
                    $this->error('设备更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            return $this->fetch();
        }
    }
}

?>