<?php
/**
 * @file            OrgAdmin.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     经销商-账号管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;


class OrgAdmin extends Base{

    public function index(){
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');
        $filter['org_id'] = input('org_id', '');
        $where = [];
        $where[] = ['t1.create_sys', '=', 1];//系统创建
        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }
        if (!empty($filter['org_id'])) {
            $where[] = ['t1.org_id', '=', $filter['org_id']];
        }
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.tel', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : 50;
        $rows = db('org_admin')->alias('t1')
            ->leftJoin("org t2","t1.org_id = t2.org_id")
            ->where($where)
            ->field('t1.*, t2.name org_name')
            ->order('t1.id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();
        $org_id = $params['org_id'];
        $create = true;
        $data = array();
        $time = time();
        // $try_days = config('try_account_days');
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('org_admin')->where('id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);
            $org_id = $row['org_id'];
            $data['id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
            //新增账号，有效期默认10天
            // $data['expire_date'] = date('Y-m-d', strtotime("+$try_days days"));
        }

        if (Request::isPost()) {
            if (!empty($params['password'])) {
                if ($params['password'] != $params['password_confirm']) {
                    $this->error('密码不一致，请重新操作！', '/Admin/info');
                }
                // $data['password'] = sha1(md5($params['password']));
                $data['password'] = getPassword($params['password']);
            }
            $data['type'] = 1;//商户管理员
            $data['login_name'] = input('login_name', '');
            $data['name'] = input('name', '');
            $data['mobile'] = input('mobile', '');
            $data['email'] = input('email', '');
            $data['org_id'] = input('org_id', '');
            $data['create_sys'] = 1;//ERP系统开通账号
            $data['role_id'] = 99;//商户超管员角色ID
            // $data['remark'] = input('remark', '');
            $data['status'] = input('status', 1);
            

            if ($create) {
                if (!db('org_admin')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['login_name', '=', $data['name']],
                    ['id', "<>", $params['id']]
                ];
                $exist = db('org_admin')->where($map)->count();
                if ($exist > 0) {
                    $this->error('账号已存在');
                }
                if (db('org_admin')->update($data) === false) {
                    $this->error('商户更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            $this->assign('org_id', $org_id);
            return $this->fetch();
        }
    }

}

?>