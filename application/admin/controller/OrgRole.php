<?php

/**
 * @file            RoleController.class.php
 * @version         1.0
 * @date            Fri, 23 Feb 2018 11:12:07 GMT
 * @description     This is the controller class for data "role"
 */
namespace app\admin\controller;

use think\Controller;
use app\admin\model\OrgRole as OrgRoleModel;
use app\admin\model\AuthRule;
use think\Db;
use think\facade\Request;

class OrgRole extends Base
{
    /**
     * 角色列表页面
     */
    public function index()
    {
        $this->assign('groupData', getOrgRoleGroupRows());
        return $this->fetch();
    }

    /**
     * 角色详情页面
     */
    public function info()
    {
        $params = input();
        $authRuleModel = model('OrgAuthRule');

        $create = true;
        $data = array();
        //新建还是编辑
        if(isset($params['id']) && !empty($params['id'])){
            $create = false;
            $role_id = $params['id'];

            //获取权限组
            $roleRow = OrgRoleModel::get($role_id);
            // var_dump($roleRow);exit;
            $roles = $roleRow->AuthRule;
            // var_dump($roles);exit;
            $roles = json_decode(json_encode($roles),true);
            $roleRow['rules'] = $roles;

            if (empty($roleRow)) {
                $this->error('信息有误，请重新操作！','/Role/index');
            }
            $this->assign('roleRow', $roleRow);

            $roleAuthRuleRows = _array_column($roleRow['rules'], 'id');
            $this->assign('roleAuthRuleRows', $roleAuthRuleRows);

            $data['id'] = $role_id;
            $data['update_time'] = time();
        }else{
            $data['create_time'] = time();
        }

        if (Request::isPost()) {
            $data['name'] = input('name','');
            $data['status'] = input('status',0);
            $data['remark'] = input('remark','');
            $data['group_id'] = input('group_id',0);
            if(empty($params["rules"])){
                $this->error('请勾选权限列表');
            }
            $auth_rule_ids = array_column($params["rules"],'id');

            if($create){
                $role_id = db('org_role')->insertGetId($data);
                if(!$role_id){
                    $this->error('用户组添加失败');
                }
            }else{
                $rmap = [
                    ['name','=',$data['name']],
                    ['id','<>',$role_id]
                ];
                $name_exist = db('org_role')->where($rmap)->count();
                if($name_exist > 0){
                    $this->error('用户组名称已存在');
                }
                //更新用户组信息
                $res = db('org_role')->update($data);
                if(!$res){
                    $this->error('用户组修改失败');
                }
            }
            //获取权限组,处理中间表数据
            $roleRow = OrgRoleModel::get($role_id);
            $roleRow->AuthRule()->detach();
            $roleRow->AuthRule()->attach($auth_rule_ids);

            $this->success("保存成功",url('index'));
        }else{
            $authRuleAll = $authRuleModel->getAuthRuleForPid();
            $this->assign('authRuleAll', $authRuleAll);
            return $this->fetch();
        }
    }
}
