<?php
/**
 * @file            Organize.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     经销商管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\service\WxService;


class Organize extends Base{

    public function index(){
        
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');

        $where = [];

        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.consignee', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.list_rows');
        $rows = db('org')->alias('t1')
            ->leftJoin("area p","t1.province_id = p.id")
            ->leftJoin("area c","t1.city_id = c.id")
            ->leftJoin("area a","t1.area_id = a.id")
            ->field("t1.*, p.area_name province_name, c.area_name city_name, a.area_name district_name")
            ->where($where)
            ->order('t1.org_id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('org')->where('org_id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);
            //省
            $provinceRows = get_area();
            $this->assign('provinceRows', $provinceRows);
            //市
            $cityRows = get_area('2', $row['province_id']);
            $this->assign('cityRows', $cityRows);
            //区
            $areaRows = get_area('3', $row['city_id']);
            $this->assign('areaRows', $areaRows);

            $data['org_id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            $data['name'] = input('name', '');
            
            $data['consignee'] = input('consignee', '');
            $data['consignee_tel'] = input('consignee_tel', '');
            $data['province_id'] = input('province_id', 0);
            $data['city_id'] = input('city_id', 0);
            $data['area_id'] = input('area_id', 0);
            $data['address'] = input('address', '');
            $data['remark'] = input('remark', '');
            $data['status'] = input('status', 1);

            if ($create) {
                if (!db('org')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['name', '=', $data['name']],
                    ['org_id', "<>", $params['id']]
                ];
                $exist = db('org')->where($map)->count();
                if ($exist > 0) {
                    $this->error('商户名称已存在');
                }
                if (db('org')->update($data) === false) {
                    $this->error('商户更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            $provinceRows = get_area();
            $this->assign('provinceRows', $provinceRows);
            return $this->fetch();
        }
    }

    /**
     * 设备关联管理
     */
    public function bind(){
        $org_id = input('org_id', 0);

        if (empty($org_id)) {
            $this->error('参数错误', url('index'));
        }

        // 获取组织信息
        $org = db('org')->where('org_id', $org_id)->find();
        if (empty($org)) {
            $this->error('组织信息不存在', url('index'));
        }

        if (Request::isPost()) {
            $action = input('action', '');
            $machine_id = input('machine_id', 0);
            $machine_ids = input('machine_ids', []);

            if ($action == 'bind' && !empty($machine_id)) {
                // 单个绑定设备
                $this->bindMachine($org_id, $machine_id);
                return json(['code' => 1, 'msg' => '绑定成功']);
            } elseif ($action == 'unbind' && !empty($machine_id)) {
                // 单个解绑设备
                $this->unbindMachine($org_id, $machine_id);
                return json(['code' => 1, 'msg' => '解绑成功']);
            } elseif ($action == 'batch_bind' && !empty($machine_ids)) {
                // 批量绑定设备
                $result = $this->batchBindMachine($org_id, $machine_ids);
                return json($result);
            } elseif ($action == 'batch_unbind' && !empty($machine_ids)) {
                // 批量解绑设备
                $result = $this->batchUnbindMachine($org_id, $machine_ids);
                return json($result);
            } elseif ($action == 'generate_qrcode' && !empty($machine_id)) {
                // 生成单个设备二维码
                $result = $this->generateMachineQRCode($machine_id);
                return json($result);
            } elseif ($action == 'batch_generate_qrcode' && !empty($machine_ids)) {
                // 批量生成设备二维码
                $result = $this->batchGenerateQRCode($machine_ids);
                return json($result);
            }

            return json(['code' => 0, 'msg' => '操作失败']);
        }

        // 获取已绑定的设备列表
        $boundMachines = db('org_machine')->alias('om')
            ->leftJoin('machine m', 'om.machine_id = m.id')
            ->leftJoin('factory f', 'm.factory_id = f.id')
            ->field('om.*, m.code, m.model, m.mac, m.desc, f.name as factory_name')
            ->where('om.org_id', $org_id)
            ->where('om.status', 1)
            ->where('m.status', 1)
            ->where('m.bind_status', 1)
            ->select();

        // 获取未绑定的设备列表
        $boundMachineIds = array_column($boundMachines, 'machine_id');
        $where = [
            ['m.bind_status', '=', 0],
            ['m.status', '=', 1]
        ];
        if (!empty($boundMachineIds)) {
            $where[] = ['m.id', 'not in', $boundMachineIds];
        }

        $unboundMachines = db('machine')->alias('m')
            ->leftJoin('factory f', 'm.factory_id = f.id')
            ->field('m.*, f.name as factory_name')
            ->where($where)
            ->select();

        $this->assign('org', $org);
        $this->assign('boundMachines', $boundMachines);
        $this->assign('unboundMachines', $unboundMachines);

        return $this->fetch();
    }

    /**
     * 绑定设备
     */
    private function bindMachine($org_id, $machine_id) {
        $time = time();

        // 检查是否已经绑定
        $exist = db('org_machine')->where([
            'org_id' => $org_id,
            'machine_id' => $machine_id
        ])->find();

        if ($exist) {
            if ($exist['status'] == 0) {
                // 如果之前解绑过，重新激活
                db('org_machine')->where('id', $exist['id'])->update([
                    'status' => 1,
                    'update_time' => $time
                ]);
                
            } else {
                return json(['code' => 0, 'msg' => '设备已经绑定']);
            }
        } else {
            // 新增绑定记录
            $data = [
                'org_id' => $org_id,
                'machine_id' => $machine_id,
                'status' => 1,
                'create_time' => $time,
                'update_time' => $time
            ];

            if (!db('org_machine')->insert($data)) {
                return json(['code' => 0, 'msg' => '绑定失败']);
            }
        }
        //更新绑定状态
        db('machine')->where('id', $machine_id)->update([
            'bind_status' => 1,
            'update_time' => $time
        ]);
    }

    /**
     * 解绑设备
     */
    private function unbindMachine($org_id, $machine_id) {
        $result = db('org_machine')->where([
            'org_id' => $org_id,
            'machine_id' => $machine_id
        ])->update([
            'status' => 0,
            'update_time' => time()
        ]);

        if ($result === false) {
            return json(['code' => 0, 'msg' => '解绑失败']);
        }
        //更新绑定状态
        db('machine')->where('id', $machine_id)->update([
            'bind_status' => 0,
            'update_time' => time()
        ]);
    }

    /**
     * 批量绑定设备
     */
    private function batchBindMachine($org_id, $machine_ids) {
        $time = time();
        $success_count = 0;
        $fail_count = 0;
        $already_bound = 0;

        foreach ($machine_ids as $machine_id) {
            // 检查是否已经绑定
            $exist = db('org_machine')->where([
                'org_id' => $org_id,
                'machine_id' => $machine_id
            ])->find();

            if ($exist) {
                if ($exist['status'] == 0) {
                    // 如果之前解绑过，重新激活
                    $result = db('org_machine')->where('id', $exist['id'])->update([
                        'status' => 1,
                        'update_time' => $time
                    ]);
                    if ($result !== false) {
                        $success_count++;
                        // 更新设备绑定状态
                        db('machine')->where('id', $machine_id)->update([
                            'bind_status' => 1,
                            'update_time' => $time
                        ]);
                    } else {
                        $fail_count++;
                    }
                } else {
                    $already_bound++;
                }
            } else {
                // 新增绑定记录
                $data = [
                    'org_id' => $org_id,
                    'machine_id' => $machine_id,
                    'status' => 1,
                    'create_time' => $time,
                    'update_time' => $time
                ];

                if (db('org_machine')->insert($data)) {
                    $success_count++;
                    // 更新设备绑定状态
                    db('machine')->where('id', $machine_id)->update([
                        'bind_status' => 1,
                        'update_time' => $time
                    ]);
                } else {
                    $fail_count++;
                }
            }
        }

        $msg = "批量绑定完成：成功 {$success_count} 个";
        if ($already_bound > 0) {
            $msg .= "，已绑定 {$already_bound} 个";
        }
        if ($fail_count > 0) {
            $msg .= "，失败 {$fail_count} 个";
        }

        return ['code' => 1, 'msg' => $msg];
    }

    /**
     * 批量解绑设备
     */
    private function batchUnbindMachine($org_id, $machine_ids) {
        $time = time();
        $success_count = 0;
        $fail_count = 0;

        foreach ($machine_ids as $machine_id) {
            $result = db('org_machine')->where([
                'org_id' => $org_id,
                'machine_id' => $machine_id
            ])->update([
                'status' => 0,
                'update_time' => $time
            ]);

            if ($result !== false) {
                $success_count++;
                // 更新设备绑定状态
                db('machine')->where('id', $machine_id)->update([
                    'bind_status' => 0,
                    'update_time' => $time
                ]);
            } else {
                $fail_count++;
            }
        }

        $msg = "批量解绑完成：成功 {$success_count} 个";
        if ($fail_count > 0) {
            $msg .= "，失败 {$fail_count} 个";
        }

        return ['code' => 1, 'msg' => $msg];
    }

    /**
     * 生成单个设备二维码
     */
    private function generateMachineQRCode($machine_id)
    {
        try {
            // 获取设备信息
            $machine = db('machine')->where('id', $machine_id)->find();
            if (!$machine) {
                return ['code' => 0, 'msg' => '设备不存在'];
            }

            $wxService = new WxService();
            $scene = 'machine_' . $machine_id;

            $result = $wxService->getUnlimitedQRCode($scene);

            // 保存二维码信息到数据库
            $this->saveMachineQRCode($machine_id, $result['filename'], $result['url'], $scene);

            return [
                'code' => 1,
                'msg' => '二维码生成成功',
                'data' => [
                    'machine_code' => $machine['code'],
                    'qrcode_url' => $result['url'],
                    'filename' => $result['filename']
                ]
            ];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '生成二维码失败: ' . $e->getMessage()];
        }
    }

    /**
     * 批量生成设备二维码
     */
    private function batchGenerateQRCode($machine_ids)
    {
        try {
            // 获取设备信息
            $machines = db('machine')->where('id', 'in', $machine_ids)->select();
            if (empty($machines)) {
                return ['code' => 0, 'msg' => '未找到设备'];
            }

            $wxService = new WxService();
            $results = $wxService->generateMachineQRCodes($machines);

            $success_count = 0;
            $fail_count = 0;

            foreach ($results as $result) {
                if ($result['success']) {
                    $success_count++;
                    // 保存二维码信息到数据库
                    $scene = 'machine_' . $result['machine_id'];
                    $this->saveMachineQRCode($result['machine_id'], $result['filename'], $result['url'], $scene);
                } else {
                    $fail_count++;
                }
            }

            $msg = "批量生成完成：成功 {$success_count} 个";
            if ($fail_count > 0) {
                $msg .= "，失败 {$fail_count} 个";
            }

            return ['code' => 1, 'msg' => $msg, 'data' => $results];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '批量生成二维码失败: ' . $e->getMessage()];
        }
    }

    /**
     * 保存设备二维码信息到数据库
     */
    private function saveMachineQRCode($machine_id, $filename, $url, $scene)
    {
        $time = time();

        // 检查是否已存在二维码记录
        $exist = db('machine_qrcode')->where('machine_id', $machine_id)->find();

        $data = [
            'machine_id' => $machine_id,
            'filename' => $filename,
            'qrcode_url' => $url,
            'scene' => $scene,
            'update_time' => $time
        ];

        if ($exist) {
            // 更新记录
            db('machine_qrcode')->where('id', $exist['id'])->update($data);
        } else {
            // 新增记录
            $data['create_time'] = $time;
            db('machine_qrcode')->insert($data);
        }
    }

}

?>