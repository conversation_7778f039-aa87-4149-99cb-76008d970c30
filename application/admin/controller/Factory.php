<?php
/**
 * @file            Factory.php
 * @version         1.0
 * @date            Thu, 22 Mar 2018 14:05:42 GMT
 * @description     工厂管理
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;


class Factory extends Base{

    public function index(){
        
        $size = request()->param('size');
        $filter['status'] = input('status', '');
        $filter['keyword'] = input('keyword', '');

        $where = [];

        if (!empty($filter['status'])) {
            $where[] = ['t1.status', '=', $filter['status']];
        }

        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t1.name|t1.consignee', 'like', "%$keyword%"];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.list_rows');
        $rows = db('factory')->alias('t1')
            ->leftJoin("area p","t1.province_id = p.id")
            ->leftJoin("area c","t1.city_id = c.id")
            ->leftJoin("area a","t1.area_id = a.id")
            ->field("t1.*, p.area_name province_name, c.area_name city_name, a.area_name district_name")
            ->where($where)
            ->order('t1.id desc')
            ->paginate($pagesize);
        // echo Db::getlastsql();exit;

        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function info(){
        $params = input();

        $create = true;
        $data = array();
        $time = time();
        //新建还是编辑
        if (isset($params['id']) && !empty($params['id'])) {
            $create = false;

            $row = db('factory')->where('id', $params['id'])->find();
            if (empty($row)) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $this->assign('row', $row);
            //省
            $provinceRows = get_area();
            $this->assign('provinceRows', $provinceRows);
            //市
            $cityRows = get_area('2', $row['province_id']);
            $this->assign('cityRows', $cityRows);
            //区
            $areaRows = get_area('3', $row['city_id']);
            $this->assign('areaRows', $areaRows);

            $data['id'] = $params['id'];
            $data['update_time'] = $time;
        } else {
            $data['create_time'] = $time;
            $data['update_time'] = $time;
        }

        if (Request::isPost()) {
            $data['name'] = input('name', '');
            $data['code'] = input('code', '');
            $data['consignee'] = input('consignee', '');
            $data['consignee_tel'] = input('consignee_tel', '');
            $data['province_id'] = input('province_id', 0);
            $data['city_id'] = input('city_id', 0);
            $data['area_id'] = input('area_id', 0);
            $data['address'] = input('address', '');
            $data['remark'] = input('remark', '');
            $data['status'] = input('status', 1);

            if ($create) {
                if (!db('factory')->insertGetId($data)) {
                    $this->error('用户添加失败');
                }
            } else {
                $map = [
                    ['name', '=', $data['name']],
                    ['id', "<>", $params['id']]
                ];
                $exist = db('factory')->where($map)->count();
                if ($exist > 0) {
                    $this->error('商户名称已存在');
                }
                if (db('factory')->update($data) === false) {
                    $this->error('商户更新失败');
                }
            }

            $this->success("保存成功", url('index'));
        } else {
            $provinceRows = get_area();
            $this->assign('provinceRows', $provinceRows);
            return $this->fetch();
        }
    }

}

?>