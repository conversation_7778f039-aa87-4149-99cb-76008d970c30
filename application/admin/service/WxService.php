<?php

namespace app\admin\service;

use think\facade\Config;
use think\facade\Cache;

class WxService
{
    private $appId;
    private $secret;
    private $mini_page;
    private $check_path;
    private $env_version;
    private $accessToken;

    public function __construct()
    {
        $this->appId = Config::get('wechat.miniprogram.app_id');
        $this->secret = Config::get('wechat.miniprogram.secret');
        $this->mini_page = Config::get('wechat.miniprogram.mini_page');
        $this->check_path = Config::get('wechat.miniprogram.check_path');
        $this->env_version = Config::get('wechat.miniprogram.env_version');
    }

    /**
     * 获取访问令牌
     * @return string|false
     */
    public function getAccessToken()
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        // 从缓存中获取
        $cacheKey = 'wechat_access_token';
        $accessToken = Cache::get($cacheKey);

        if (!$accessToken) {
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->secret}";

            $response = $this->httpGet($url);
            $data = json_decode($response, true);

            if (isset($data['access_token'])) {
                $accessToken = $data['access_token'];
                // 缓存access_token，提前5分钟过期
                Cache::set($cacheKey, $accessToken, $data['expires_in'] - 300);
            } else {
                throw new \Exception('获取access_token失败: ' . ($data['errmsg'] ?? '未知错误'));
            }
        }

        $this->accessToken = $accessToken;
        return $accessToken;
    }

    /**
     * 生成小程序码（无限制）
     * @param string $scene 场景值，最大32个可见字符
     * @param string $page 页面路径，不能带参数
     * @param int $width 二维码宽度，默认430
     * @param bool $autoColor 自动配置线条颜色
     * @param array $lineColor 线条颜色，当auto_color为false时生效
     * @param bool $isHyaline 是否需要透明底色
     * @return array
     */
    public function getUnlimitedQRCode($scene, $page = null, $width = 430, $autoColor = true, $lineColor = ['r' => 0, 'g' => 0, 'b' => 0], $isHyaline = false)
    {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={$accessToken}";

        $data = [
            'scene' => $scene,
            'width' => $width,
            'auto_color' => $autoColor,
            'is_hyaline' => $isHyaline,
            'page' => $this->mini_page,
            'check_path' => $this->check_path,
            'env_version' => $this->env_version
        ];

        if (!$autoColor) {
            $data['line_color'] = $lineColor;
        }

        $response = $this->httpPost($url, json_encode($data));

        // 检查是否返回错误信息
        $jsonData = json_decode($response, true);
        if ($jsonData && isset($jsonData['errcode'])) {
            throw new \Exception('生成小程序码失败: ' . $jsonData['errmsg']);
        }

        // 保存二维码图片
        $filename = $this->saveQRCodeImage($response, $scene);

        return [
            'success' => true,
            'filename' => $filename,
            'url' => $this->getQRCodeUrl($filename),
            'scene' => $scene
        ];
    }

    /**
     * 生成小程序二维码（有限制）
     * @param string $path 页面路径，可以带参数
     * @param int $width 二维码宽度，默认430
     * @return array
     */
    public function getQRCode($path, $width = 430)
    {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$accessToken}";

        $data = [
            'path' => $path,
            'width' => $width
        ];

        $response = $this->httpPost($url, json_encode($data));

        // 检查是否返回错误信息
        $jsonData = json_decode($response, true);
        if ($jsonData && isset($jsonData['errcode'])) {
            throw new \Exception('生成小程序二维码失败: ' . $jsonData['errmsg']);
        }

        // 保存二维码图片
        $filename = $this->saveQRCodeImage($response, md5($path));

        return [
            'success' => true,
            'filename' => $filename,
            'url' => $this->getQRCodeUrl($filename),
            'path' => $path
        ];
    }

    /**
     * 保存二维码图片
     * @param string $imageData 图片二进制数据
     * @param string $scene 场景值或标识
     * @return string 文件名
     */
    private function saveQRCodeImage($imageData, $scene)
    {
        $savePath = Config::get('wechat.qrcode.save_path', 'static/qrcode/');
        $fullPath = env('root_path') . 'public/' .$savePath;

        // 创建目录
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // 生成文件名
        $filename = 'qrcode_' . $scene . '_' . date('YmdHis') . '_' . uniqid() . '.jpg';
        $filePath = $fullPath . $filename;

        // 保存文件
        if (file_put_contents($filePath, $imageData) === false) {
            throw new \Exception('保存二维码图片失败');
        }

        return $filename;
    }

    /**
     * 获取二维码访问URL
     * @param string $filename 文件名
     * @return string
     */
    private function getQRCodeUrl($filename)
    {
        $savePath = Config::get('wechat.qrcode.save_path', 'static/qrcode/');
        $urlPrefix = Config::get('wechat.qrcode.url_prefix', '');

        if (empty($urlPrefix)) {
            $urlPrefix = request()->domain();
        }

        return rtrim($urlPrefix, '/') . '/' . ltrim($savePath, '/') . $filename;
    }

    /**
     * 发送GET请求
     * @param string $url
     * @return string
     */
    private function httpGet($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception('HTTP请求失败: ' . $error);
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception('HTTP请求失败，状态码: ' . $httpCode);
        }

        return $response;
    }

    /**
     * 发送POST请求
     * @param string $url
     * @param string $data
     * @return string
     */
    private function httpPost($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception('HTTP请求失败: ' . $error);
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new \Exception('HTTP请求失败，状态码: ' . $httpCode);
        }

        return $response;
    }

    /**
     * 清理过期的二维码文件
     * @return int 清理的文件数量
     */
    public function cleanExpiredQRCodes()
    {
        $savePath = Config::get('wechat.qrcode.save_path', 'static/qrcode/');
        $fullPath = env('root_path') . 'public/' . $savePath;
        $expireSeconds = Config::get('wechat.qrcode.expire_seconds', 2592000);

        if (!is_dir($fullPath)) {
            return 0;
        }

        $count = 0;
        $files = scandir($fullPath);
        $now = time();

        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $filePath = $fullPath . $file;
            if (is_file($filePath)) {
                $fileTime = filemtime($filePath);
                if (($now - $fileTime) > $expireSeconds) {
                    if (unlink($filePath)) {
                        $count++;
                    }
                }
            }
        }

        return $count;
    }

    /**
     * 批量生成设备绑定二维码
     * @param array $machines 设备列表
     * @return array
     */
    public function generateMachineQRCodes($machines)
    {
        $results = [];

        foreach ($machines as $machine) {
            try {
                $scene = 'machine_' . $machine['id'];
                $result = $this->getUnlimitedQRCode($scene);
                $result['machine_id'] = $machine['id'];
                $result['machine_code'] = $machine['code'];
                $results[] = $result;
            } catch (\Exception $e) {
                $results[] = [
                    'success' => false,
                    'machine_id' => $machine['id'],
                    'machine_code' => $machine['code'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}
