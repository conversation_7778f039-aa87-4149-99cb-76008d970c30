<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

// 应用公共文件


/**
 * 获取返回页面URL
 */
function get_back_url($default_url = null)
{
    $back_url = empty($_REQUEST['back_url']) ? $_SERVER['HTTP_REFERER'] : $_REQUEST['back_url'];
    return $back_url;
}

function getPassword($pwd)
{
    return password_hash(trim($pwd), PASSWORD_DEFAULT);
}

/**
 * 获取用户组分组数据
 * @param  [type] $status [description]
 * @param  [type] $field  [description]
 * @return [type]         [description]
 */
function getRoleGroup($status = null, $field = null)
{
    $list = array(
        1 => array('id' => '1', 'name' => '总部中心'),
        // 2 => array('id'=>'2', 'name'=>'营销中心'),
    );
    if (empty($status) and empty($field)) {
        return $list;
    } else {
        if (empty($field)) {
            $field = 'name';
        }
        return $list[$status][$field];
    }
}

/**
 * 获取分组后的用户组数据
 * @return [type] [description]
 */
function getRoleGroupRows()
{
    //查询分组下的用户组
    $group = getRoleGroup();
    foreach ($group as $key => $value) {
        $map['status'] = 1;
        $map['group_id'] = $value['id'];
        $child = db('role')->where($map)->select();
        if (empty($child)) {
            unset($group[$key]);
            continue;
        }
        $group[$key]['child'] = $child;
    }
    $group = array_values($group);

    //拼接无分组的用户组
    $b_map['status'] = 1;
    $b_map['group_id'] = 0;
    $no_group_child = db('role')->where($b_map)->select();
    if (!empty($no_group_child)) {
        $num = count($group);
        $group[$num]['id'] = 0;
        $group[$num]['name'] = '无分组数据';
        $group[$num]['child'] = $no_group_child;
    }

    return $group;
}

/**
 * 获取用户组分组数据
 * @param  [type] $status [description]
 * @param  [type] $field  [description]
 * @return [type]         [description]
 */
function getOrgRoleGroup($status = null, $field = null)
{
    $list = array(
        1 => array('id' => '1', 'name' => '平台角色组'),
        2 => array('id' => '2', 'name' => '经销商角色组'),
    );
    if (empty($status) and empty($field)) {
        return $list;
    } else {
        if (empty($field)) {
            $field = 'name';
        }
        return $list[$status][$field];
    }
}

/**
 * 获取分组后的用户组数据
 * @return [type] [description]
 */
function getOrgRoleGroupRows()
{
    //查询分组下的用户组
    $group = getOrgRoleGroup();
    foreach ($group as $key => $value) {
        $map = [];
        $map['status'] = 1;
        $map['group_id'] = $value['id'];
        $map['org_id'] = 0;
        $child = db('org_role')->where($map)->select();
        if (empty($child)) {
            unset($group[$key]);
            continue;
        }
        $group[$key]['child'] = $child;
    }
    $group = array_values($group);

    //拼接无分组的用户组
    $b_map['status'] = 1;
    $b_map['group_id'] = 0;
    $no_group_child = db('org_role')->where($b_map)->select();
    if (!empty($no_group_child)) {
        $num = count($group);
        $group[$num]['id'] = 0;
        $group[$num]['name'] = '无分组数据';
        $group[$num]['child'] = $no_group_child;
    }

    return $group;
}

/**
 * 返回数组中指定的一列
 * @param $input            需要取出数组列的多维数组（或结果集）
 * @param $columnKey        需要返回值的列，它可以是索引数组的列索引，或者是关联数组的列的键。 也可以是NULL，此时将返回整个数组（配合index_key参数来重置数组键的时候，非常管用）
 * @param null $indexKey    作为返回数组的索引/键的列，它可以是该列的整数索引，或者字符串键值。
 * @return array            返回值
 */
function _array_column($input, $columnKey, $indexKey = null)
{
    if (!function_exists('array_column')) {
        $columnKeyIsNumber = (is_numeric($columnKey)) ? true : false;
        $indexKeyIsNull = (is_null($indexKey)) ? true : false;
        $indexKeyIsNumber = (is_numeric($indexKey)) ? true : false;
        $result = array();
        foreach ((array)$input as $key => $row) {
            if ($columnKeyIsNumber) {
                $tmp = array_slice($row, $columnKey, 1);
                $tmp = (is_array($tmp) && !empty($tmp)) ? current($tmp) : null;
            } else {
                $tmp = isset($row[$columnKey]) ? $row[$columnKey] : null;
            }
            if (!$indexKeyIsNull) {
                if ($indexKeyIsNumber) {
                    $key = array_slice($row, $indexKey, 1);
                    $key = (is_array($key) && !empty($key)) ? current($key) : null;
                    $key = is_null($key) ? 0 : $key;
                } else {
                    $key = isset($row[$indexKey]) ? $row[$indexKey] : 0;
                }
            }
            $result[$key] = $tmp;
        }
        return $result;
    } else {
        return array_column($input, $columnKey, $indexKey);
    }
}

/**
 * 获取列表序列号
 */
function get_sequence($page_row = 1, $idx)
{
    if (isset($_REQUEST['p']) and !empty($_REQUEST['p'])) {
        $page_no = $_REQUEST['p'] - 1;
    } else {
        $page_no = 0;
    }
    $seq = $page_no * $page_row + $idx;
    return $seq;
}


/**
 * 获取省市信息
 */
function get_area($area_level = 1, $parent_id = 0, $area_id = 0, $is_show = 0)
{
    $where = "1=1";

    if (!empty($area_level)) {
        $where .= " and level = $area_level";
    }

    if (isset($parent_id) && !empty($parent_id)) {
        $where .= " and parent_id = '$parent_id'";
    }

    if (isset($area_id) && !empty($area_id)) {
        $where .= " and id = '$area_id'";
    }

    $citys = db("area")->where("$where")->field("id,parent_id,area_name,short_name,level,is_show")->select();
    //echo  Db::getlastsql();exit;
    return $citys;
}

/**
 * 获取城市信息
 */

function get_region($level, $parent_id, $id)
{

    $where = "is_show = 0";


    if ($level) {
        $where .= " and level = $level";
    }

    if (isset($parent_id) && $parent_id != '') {
        $where .= " and parent_id = $parent_id";
    }

    if (isset($id) && $id != '') {
        $where .= " and id = $id";
    }

    $citys = db("area")
        ->field("id,parent_id,area_name,level,is_show,sort")
        ->where("$where")->select();

    return $citys;
}

/**
 * 根据id获取省市区
 */
function get_city_name($id)
{
    if (!empty($id)) {
        $where = "id = $id";
        $citys = Db::name("area")->field("id,area_name")->where($where)->find();
        return $citys['area_name'];
    }
}

/**
 * 根据商户列表
 */
function get_org_list()
{
    return db("org")->field("org_id,name")->where("status = 1")->select();
}

/**
 * 根据工厂列表
 */
function get_factory_list()
{
    return db("factory")->field("id,name")->where("status = 1")->select();
}

/**
 * 计算有限天数
 */
function get_diff_days($t1, $t2 = null)
{
    if ($t2 == null) {
        $t2 = date("Y-m-d");
    }
    return (strtotime($t1) - strtotime($t2)) / 86400;
}

/**
 *  商品状态
 */
function get_product_status($status = null, $field = null)
{
    $list = array(
        1 => array('key' => '1', 'val' => '上架', 'tag' => 'navy'),
        2 => array('key' => '2', 'val' => '下架', 'tag' => 'danger'),
    );
    if (empty($status) and empty($field)) {
        return $list;
    } else {
        if (empty($field)) {
            $field = 'val';
        }
        return $list[$status][$field];
    }
}


/* 获取产品分类
 * @param  [type] $cat_id [description]
 * @return [type]         [description]
 */
function get_product_cate($cat_id = null)
{
    if (!empty($cat_id)) {
        $data = db("product_cat")->where("cat_id", $cat_id)->find();
    } else {
        $where['status'] = 1;
        $data = db("product_cat")->where($where)->order("sort desc")->select();
    }
    return $data;
}
