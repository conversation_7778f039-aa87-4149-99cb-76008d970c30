<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>商户登录账号管理</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="{{:url('index')}}">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off">
                <div class="form-group">
                    <label class="col-sm-2 control-label">商户</label>
                    <div class="col-sm-3">
                        <select class="form-control m-b" id="org_id" name="org_id">
                            <option value="">选择商户</option>
                            {{volist name=":get_org_list()" id="v"}}
                                <option value="{{$v['org_id']}}" {{if $v['org_id'] == $org_id}}selected='selected'{{/if}}>{{$v['name']}}</option>
                            {{/volist}}
                        </select>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">登录账号（仅限邮箱）</label>
                    <div class="col-sm-3">
                        <input type="text" name="login_name" id="login_name" class="form-control" value="{{$row['login_name']}}" required />
                        <div class="error text-danger" id="errorMsg"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">姓名</label>
                    <div class="col-sm-3">
                        <input type="text" name="name" class="form-control" value="{{$row['name']}}" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">电话</label>
                    <div class="col-sm-3">
                        <input type="text" name="mobile" class="form-control" value="{{$row['mobile']}}" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">补充信息</label>
                    <div class="col-sm-3">
                        <input type="text" name="email" class="form-control" value="{{$row['email']}}" />
                    </div>
                </div>

                <div class="form-group">
                    <label for="input-password" class="col-sm-2 control-label">密码</label>
                    <div class="col-sm-3">
                        {{present name="row"}}
                            <input type="password" class="form-control" name="password" id="input-password" placeholder="不用修改时,请留空！"/>
                        {{else /}}
                            <input type="password" class="form-control" name="password" id="input-password" required/>
                        {{/present}}
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="input-c-password" class="col-sm-2 control-label">确认密码</label>
                    <div class="col-sm-3">
                        {{present name="row"}}
                            <input type="password" class="form-control" name="password_confirm" data-confirm="#input-password" placeholder="不用修改时,请留空！" data-confirm="#input-password" id="input-c-password" />
                        {{else /}}
                            <input type="password" class="form-control" name="password_confirm" data-confirm="#input-password" id="input-c-password" />
                        {{/present}}
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">启用</label>
                    <div class="col-sm-3">
                        <div class="radio radio-inline radio-success">
                            <input id="status-1" type="radio" name="status" value="1" {{if $row.status == 1}}checked="checked"{{/if}}/>
                            <label for="status-1">是</label>
                        </div>
                        <div class="radio radio-inline radio-danger">
                            <input id="status-0" type="radio" name="status" value="4" {{if $row.status == 4}}checked="checked"{{/if}}/>
                            <label for="status-0">否</label>
                        </div>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="id" value="{{$row.id}}">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<script type="text/javascript">
    $(document).ready(function(){
        // 定义一个正则表达式来匹配邮箱格式
        var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
     
        // 绑定一个事件到输入框的blur事件，当输入框失去焦点时触发
        $('#login_name').blur(function(){
            var email = $(this).val(); // 获取输入框的值
            if(!emailPattern.test(email)){
                // 如果邮箱格式不正确，显示错误信息
                $(this).css('border-color', 'red');
                $('#errorMsg').text('请输入有效的邮箱地址。');
            } else {
                // 如果邮箱格式正确，清除错误信息
                $(this).css('border-color', 'green');
                $('#errorMsg').text('');
            }
        });
    });
</script>
</body>
</html>