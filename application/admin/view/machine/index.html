<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
    <!-- <link href="__PUBLIC__/css/sweetalert.css" rel="stylesheet"> -->
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>设备管理</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-2 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">状态</span>
                                        <select name="status" class="form-control">
                                            <option value="" >全部</option>
                                            <option value="1" {{if $filter['status'] == '1'}} selected='selected'{{/if}}>启用</option>
                                            <option value="4" {{if $filter['status'] == '4'}} selected='selected'{{/if}}>禁用</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <input type="text" class="form-control" placeholder="型号/MAC" name="keyword" value="{{$filter.keyword}}" >
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2 m-b clearfix">
                        <!-- <button class="btn btn-info btn-sm pull-right" id="batch-qrcode-btn" disabled style="margin-left: 5px;">
                            <i class="fa fa-qrcode"></i> 批量生成二维码
                        </button> -->
                        <a class="btn btn-primary pull-right" href="{{:url('info')}}"><i class="fa fa-plus"></i> 添加</a>
                    </div>
                </div>
                <form action="{{:url('/Ajax/create_qrcode')}}" method="post" autocomplete="off">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th class="col-sm-1">
                            <div class="checkbox checkbox-inline">
                                <input id="SAL" type="checkbox" class="check-all">
                                <label for="SAL">全选</label>
                            </div>
                        </th>
                        <th>工厂</th>
                        <th>设备编码</th>
                        <th>设备型号</th>
                        <th>MAC地址</th>
                        <th>详细信息</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>状态</th>
                        <th class="col-sm-3">操作</th>
                    </tr>
                    </thead>
                    <tfoot style="display:none;">
                        <tr>
                            <td colspan="10">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="input-group has-success">
                                            <select name="create_qrcode" class="form-control">
                                                <option value="1">生成二维码</option>
                                            </select>
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-primary">批量处理</button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tfoot>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td>
                                <div class="checkbox checkbox-inline">
                                    <input id="SAL{{$v.id}}" type="checkbox" class="check-child" name="machine_id[]" machine-id="{{$v.id}}" value="{{$v.id}}" />
                                    <label for="SAL{{$v.id}}">{{$v.id}}</label>
                                </div>
                            </td>
                            <td>{{$v.factory_name}}</td>
                            <td>{{$v.code}}</td>
                            <td>{{$v.model}}</td>
                            <td>{{$v.mac}}</td>
                            <td>{{$v.desc}}</td>
                            <td>{{$v.create_time|date="Y-m-d"}}</td>
                            <td>{{$v.update_time|date="Y-m-d H:i:s"}}</td>
                            <td>
                                {{if $v['status'] == 1}}
                                    <span class="text-navy">启用</span>
                                {{else/}}
                                    <span class="text-danger">禁用</span>
                                {{/if}}
                            </td>
                            <td>
                                <button class="btn btn-xs btn-warning view-qrcode-btn"
                                        data-machine-id="{{$v.id}}"
                                        data-machine-code="{{$v.code}}"
                                        title="查看二维码">
                                    <i class="fa fa-eye"></i>
                                </button>
                                <a class="btn btn-xs btn-success" href="{{:url('info',array('id'=>$v['id']))}}">
                                    <i class="fa fa-edit"></i> 编辑
                                </a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>
                </form>
                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>
    <!-- <script src="__PUBLIC__/js/sweetalert.min.js"></script> -->

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>

    <script>
        $(document).ready(function() {

            var checkbox = $('.check-all, .check-child');
            checkbox.on('click', function(){
                var obj = batch();
                if(obj.num < 1){
                    $('tfoot').hide();
                }else{
                    $('tfoot').show();
                }
            });
            var submit = $('tfoot').find('button');
            submit.on('click', function(){
                var checkedBoxes = $('.check-child:checked');
                if (checkedBoxes.length === 0) {
                    swal('提示', '请选择要生成二维码的设备', 'warning');
                    return;
                }

                var machineIds = [];
                checkedBoxes.each(function() {
                    machineIds.push($(this).data('machine-id'));
                });

                swal({
                    title: "批量生成二维码",
                    text: "确认为选中的 " + machineIds.length + " 个设备生成二维码？",
                    type: "info",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    confirmButtonText: "确认生成",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function() {
                    var btn = $('#batch-qrcode-btn');
                    btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 生成中...');

                    $.post('{{:url("qrcode")}}', {
                        action: 'batch_generate_qrcode',
                        machine_ids: machineIds
                    }, function(data) {
                        if (data.code == 1) {
                            swal("批量生成完成", data.msg, "success");
                        } else {
                            swal("生成失败", data.msg || '批量生成二维码失败', "error");
                        }
                    }, 'json').always(function() {
                        btn.prop('disabled', false).html('<i class="fa fa-qrcode"></i> 批量生成二维码');
                        updateBatchButtons();
                    });
                });
            });
            function batch(){
                var obj = {}; obj.num = 0;
                $('.check-child:checked').each(function(){
                    obj.num++;
                });
                return obj;
            }

            // 单个设备生成二维码
            $(document).on('click', '.qrcode-btn', function() {
                var machineId = $(this).data('machine-id');
                var machineCode = $(this).data('machine-code');
                var btn = $(this);

                swal({
                    title: "生成二维码",
                    text: "确认为设备 " + machineCode + " 生成二维码？",
                    type: "info",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    confirmButtonText: "确认生成",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function() {
                    btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');

                    $.post('{{:url("qrcode")}}', {
                        action: 'generate_qrcode',
                        machine_id: machineId
                    }, function(data) {
                        if (data.code == 1) {
                            var content = '<div style="text-align: center;">';
                            content += '<p>设备编码：' + data.data.machine_code + '</p>';
                            content += '<p><img src="' + data.data.qrcode_url + '" style="max-width: 200px;" /></p>';
                            content += '<p><a href="' + data.data.qrcode_url + '" target="_blank">查看原图</a></p>';
                            content += '</div>';

                            swal({
                                title: "二维码生成成功",
                                text: content,
                                html: true,
                                type: "success"
                            });
                        } else {
                            swal("生成失败", data.msg || '生成二维码失败', "error");
                        }
                    }, 'json').always(function() {
                        btn.prop('disabled', false).html('<i class="fa fa-qrcode"></i>');
                    });
                });
            });

            // 查看设备二维码
            $(document).on('click', '.view-qrcode-btn', function() {
                var machineId = $(this).data('machine-id');
                var machineCode = $(this).data('machine-code');
                var btn = $(this);

                btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');

                $.post('{{:url("qrcode")}}', {
                    action: 'get_qrcode',
                    machine_id: machineId
                }, function(data) {
                    if (data.code == 1) {
                        var content = '<div style="text-align: center;">';
                        content += '<p>设备编码：' + data.data.machine_code + '</p>';
                        content += '<p>场景值：' + data.data.scene + '</p>';
                        content += '<p>生成时间：' + data.data.create_time + '</p>';
                        content += '<p><img src="' + data.data.qrcode_url + '" style="max-width: 200px;" /></p>';
                        content += '<p><a href="' + data.data.qrcode_url + '" target="_blank">查看原图</a></p>';
                        content += '</div>';

                        swal({
                            title: "设备二维码",
                            text: content,
                            html: true,
                            type: "info"
                        });
                    } else {
                        swal("查看失败", data.msg || '获取二维码失败', "warning");
                    }
                }, 'json').always(function() {
                    btn.prop('disabled', false).html('<i class="fa fa-eye"></i>');
                });
            });

            // 批量生成二维码
            $('#batch-qrcode-btn').click(function() {
                var checkedBoxes = $('.machine-check:checked');
                if (checkedBoxes.length === 0) {
                    swal('提示', '请选择要生成二维码的设备', 'warning');
                    return;
                }

                var machineIds = [];
                checkedBoxes.each(function() {
                    machineIds.push($(this).data('machine-id'));
                });

                swal({
                    title: "批量生成二维码",
                    text: "确认为选中的 " + machineIds.length + " 个设备生成二维码？",
                    type: "info",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    confirmButtonText: "确认生成",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function() {
                    var btn = $('#batch-qrcode-btn');
                    btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 生成中...');

                    $.post('{{:url("qrcode")}}', {
                        action: 'batch_generate_qrcode',
                        machine_ids: machineIds
                    }, function(data) {
                        if (data.code == 1) {
                            swal("批量生成完成", data.msg, "success");
                        } else {
                            swal("生成失败", data.msg || '批量生成二维码失败', "error");
                        }
                    }, 'json').always(function() {
                        btn.prop('disabled', false).html('<i class="fa fa-qrcode"></i> 批量生成二维码');
                        updateBatchButtons();
                    });
                });
            });

        });
    </script>
</body>
</html>
