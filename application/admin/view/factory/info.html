<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>工厂管理</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="{{:url('index')}}">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off">

                <div class="form-group">
                    <label class="col-sm-2 control-label">工厂名称</label>
                    <div class="col-sm-3">
                        <input type="text" name="name" class="form-control" value="{{$row['name']}}" required />
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">设备编码</label>
                    <div class="col-sm-3">
                        <input type="text" name="code" class="form-control" value="{{$row['code']}}" required />
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">商户联系人</label>
                    <div class="col-sm-3">
                        <input type="text" name="consignee" class="form-control" value="{{$row.consignee}}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">联系人号码</label>
                    <div class="col-sm-3">
                        <input type="text" name="consignee_tel" class="form-control" value="{{$row.consignee_tel}}"/>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">省市区</label>
                    <div class="col-sm-2">
                        <select class="form-control" id="province_id" name="province_id" required>
                            <option value="">选择省份</option>
                            {{volist name="provinceRows" id="v"}}
                            <option value="{{$v.id}}" {{if condition="$v.id eq $row['province_id']"}} selected='select'{{/if}}>{{$v.area_name}}</option>
                            {{/volist}}
                        </select>
                    </div>
                    <div class="col-sm-2">
                        {{empty name = "cityRows"}}
                            <select class="form-control  select" id="city_id" name="city_id">
                                <option value="">选择城市</option>
                            </select>
                        {{else/}}
                            <select class="form-control  select" id="city_id" name="city_id">
                                <option value=''>请选择</option>
                                {{volist name="$cityRows" id="v" }}
                                <option value="{{$v.id}}" {{if condition="$v.id eq $row['city_id']"}} selected='select'{{/if}}>{{$v.area_name}}</option>
                                {{/volist}}
                            </select>
                        {{/empty}}
                    </div>
                    <div class="col-sm-2">
                        {{empty name = "areaRows"}}
                        <select class="form-control  select" id="district_id" name="area_id">
                            <option value="">选择区县</option>
                        </select>
                        {{else/}}
                            <select class="form-control  select" id="district_id" name="area_id">
                                <option value=''>请选择</option>
                                {{volist name="$areaRows" id="v" }}
                                <option value="{{$v.id}}" {{if condition="$v.id eq $row['area_id']"}} selected='select'{{/if}}>{{$v.area_name}}</option>
                                {{/volist}}
                            </select>
                        {{/empty}}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">详细地址</label>
                    <div class="col-sm-3">
                        <input type="text" name="address" class="form-control" value="{{$row['address']}}" required />
                    </div>
                </div>
                <div class="hr-line-dashed"></div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">备注</label>
                    <div class="col-sm-3">
                        <textarea rows='4' name="remark" class="form-control">{{$row['remark']}}</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">启用</label>
                    <div class="col-sm-3">
                        <div class="radio radio-inline radio-success">
                            <input id="status-1" type="radio" name="status" value="1" {{if $row.status == 1}}checked="checked"{{/if}}/>
                            <label for="status-1">是</label>
                        </div>
                        <div class="radio radio-inline radio-danger">
                            <input id="status-0" type="radio" name="status" value="4" {{if $row.status == 4}}checked="checked"{{/if}}/>
                            <label for="status-0">否</label>
                        </div>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="id" value="{{$row.org_id}}">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>





            

