<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备关联管理</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v={{$time}}" rel="stylesheet">
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="ibox">
            <div class="ibox-title">
                <h5>设备关联管理 - {{$org.name}}</h5>
                <div class="ibox-tools">
                    <a id="page-goback" href="{{:url('index')}}">
                        <i class="fa fa-arrow-left"></i> 后退
                    </a>
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <!-- 已绑定设备 -->
                    <div class="col-md-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h3 class="panel-title">已绑定设备</h3>
                            </div>
                            <div class="panel-body">
                                {{if !empty($boundMachines)}}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>设备编码</th>
                                                <th>设备型号</th>
                                                <th>厂家</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{volist name="boundMachines" id="machine"}}
                                            <tr>
                                                <td>{{$machine.code}}</td>
                                                <td>{{$machine.model}}</td>
                                                <td>{{$machine.factory_name}}</td>
                                                <td>
                                                    <button class="btn btn-danger btn-xs unbind-btn"
                                                        data-machine-id="{{$machine.machine_id}}"
                                                        data-machine-code="{{$machine.code}}">
                                                        <i class="fa fa-unlink"></i> 解绑
                                                    </button>
                                                </td>
                                            </tr>
                                            {{/volist}}
                                        </tbody>
                                    </table>
                                </div>
                                {{else/}}
                                <div class="text-center text-muted">
                                    <i class="fa fa-info-circle fa-2x"></i>
                                    <p>暂无绑定设备</p>
                                </div>
                                {{/if}}
                            </div>
                        </div>
                    </div>

                    <!-- 可绑定设备 -->
                    <div class="col-md-6">
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h3 class="panel-title">可绑定设备</h3>
                            </div>
                            <div class="panel-body">
                                {{if !empty($unboundMachines)}}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>设备编码</th>
                                                <th>设备型号</th>
                                                <th>厂家</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{volist name="unboundMachines" id="machine"}}
                                            <tr>
                                                <td>{{$machine.code}}</td>
                                                <td>{{$machine.model}}</td>
                                                <td>{{$machine.factory_name}}</td>
                                                <td>
                                                    <button class="btn btn-success btn-xs bind-btn" 
                                                            data-machine-id="{{$machine.id}}"
                                                            data-machine-code="{{$machine.code}}">
                                                        <i class="fa fa-link"></i> 绑定
                                                    </button>
                                                </td>
                                            </tr>
                                            {{/volist}}
                                        </tbody>
                                    </table>
                                </div>
                                {{else/}}
                                <div class="text-center text-muted">
                                    <i class="fa fa-info-circle fa-2x"></i>
                                    <p>暂无可绑定设备</p>
                                </div>
                                {{/if}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>

    <script>
        $(document).ready(function () {
            // 绑定设备
            $('.bind-btn').click(function () {
                var machineId = $(this).data('machine-id');
                var machineCode = $(this).data('machine-code');

                swal({
                    title: "绑定提示",
                    text: "确认绑定，请谨慎操作",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确认绑定",
                    cancelButtonText: "暂不绑定",
                    closeOnConfirm: false
                },
                function () {
                    $.post('{{:url("bind", ["org_id" => $org.org_id])}}', {
                        action: 'bind',
                        machine_id: machineId
                    }, function (data) {
                        if (data.code == 1) {
                            location.reload();
                        } else {
                            _alert(data.msg || '绑定失败');
                        }
                    }, 'json');
                });
            });

            // 解绑设备
            $('.unbind-btn').click(function () {
                var machineId = $(this).data('machine-id');
                var machineCode = $(this).data('machine-code');

                swal({
                    title: "解绑提示",
                    text: "确认解绑，请谨慎操作",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确认解绑",
                    cancelButtonText: "暂不解绑",
                    closeOnConfirm: false
                },
                function () {
                    $.post('{{:url("bind", ["org_id" => $org.org_id])}}', {
                        action: 'unbind',
                        machine_id: machineId
                    }, function (data) {
                        if (data.code == 1) {
                            location.reload();
                        } else {
                            _alert(data.msg || '解绑失败');
                        }
                    }, 'json');
                });
            });
        });
    </script>
</body>

</html>