<?php
/**
 *权限Model类
 */

namespace app\admin\model;

use think\Model;
use think\Db;
use think\facade\Request;

class OrgAuthRule extends Model
{
    protected $table = 'fzh_org_auth_rule';

    /**
     * 关联模型
     */
    public function Role()
    {
        return $this->belongsToMany('OrgRole','\\app\\admin\\model\\OrgRoleAuthRule');
    }

    /**
     * [getAuthRuleForPid description]
     */
    public function getAuthRuleForPid($pid = 0)
    {
        $authRuleRows = $this->where(array(
            'pid' => $pid,
        ))->order('sort Asc')->select();
        $authRuleAll = array();

        if ($authRuleRows) {
            foreach ($authRuleRows as $key => $value) {
                $authRuleAll[$value['id']]          = $value;
                $authRuleAll[$value['id']]['child'] = $this->getAuthRuleForPid($value['id']);
            }
        }

        return $authRuleAll;
    }

    /**
     * [getAuthRuleForRoleAuthRule description]
     */
    public function getAuthRuleForRoleAuthRule($roleAuthRuleRows, $pid = 0)
    {
        $map = [
            ['pid','=',$pid],
            ['islink','=',1],
            ['id','in',$roleAuthRuleRows]
        ];
        $authRuleRows = $this->where($map)->order('sort','asc')->select();
        $authRuleAll = array();

        if ($authRuleRows) {
            foreach ($authRuleRows as $key => $value) {
                $authRuleAll[$key]          = $value;
                $authRuleAll[$key]['name']  = '/' . $value['name'];
                $authRuleAll[$key]['child'] = $this->getAuthRuleForRoleAuthRule($roleAuthRuleRows, $value['id']);
            }
        }

        return $authRuleAll;
    }

    /**
     * [getBreadcrumb description]
     */
    public function getBreadcrumb()
    {
        $name           = Request::controller() . '/' . Request::controller();
        $postionLastRow = $this->getByName($name);

        if ($postionLastRow['pid'] != 0) {
            $postionFirstRow = $this->find($postionLastRow['pid']);
        }
        return array($postionFirstRow, $postionLastRow);
    }

}
