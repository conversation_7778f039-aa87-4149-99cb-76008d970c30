# 设备管理页面二维码功能使用说明

## 功能概述

在设备管理页面新增了微信小程序二维码生成和查看功能，支持：

1. **单个设备二维码生成** - 为单个设备生成专属小程序二维码
2. **批量设备二维码生成** - 同时为多个设备批量生成二维码
3. **二维码查看功能** - 查看已生成的设备二维码
4. **二维码管理** - 自动保存和管理二维码文件

## 使用方法

### 1. 单个设备操作

在设备列表中，每个设备行都有以下操作按钮：

- **蓝色二维码按钮** <i class="fa fa-qrcode"></i> - 生成二维码
- **黄色眼睛按钮** <i class="fa fa-eye"></i> - 查看已生成的二维码
- **绿色编辑按钮** <i class="fa fa-edit"></i> - 编辑设备信息

#### 生成二维码：
1. 点击设备行的蓝色二维码按钮
2. 确认生成操作
3. 系统会显示生成的二维码图片
4. 可以点击"查看原图"查看完整尺寸

#### 查看二维码：
1. 点击设备行的黄色眼睛按钮
2. 系统会显示该设备的二维码信息
3. 包含设备编码、场景值、生成时间和二维码图片

### 2. 批量操作

#### 批量生成二维码：
1. 勾选要生成二维码的设备（可多选）
2. 点击页面右上角的"批量生成二维码"按钮
3. 确认批量生成操作
4. 系统会显示批量生成结果

#### 全选功能：
- 点击表头的复选框可以全选/取消全选所有设备
- 批量操作按钮会根据选择状态自动启用/禁用

## 技术特点

### 1. 二维码参数
- **场景值格式**: `machine_{设备ID}`
- **小程序页面**: 默认跳转到设备绑定页面
- **二维码尺寸**: 430px（可在代码中调整）
- **文件格式**: PNG图片

### 2. 存储管理
- 二维码图片保存在 `public/static/upload/qrcode/` 目录
- 二维码信息存储在 `fzh_machine_qrcode` 数据表
- 支持重复生成（会覆盖旧的二维码）

### 3. 错误处理
- 完善的错误提示机制
- 网络异常处理
- 文件不存在检查

## 配置要求

### 1. 环境配置
确保在 `.env` 文件中配置了微信小程序参数：
```env
WECHAT_MINIPROGRAM_APPID = 你的小程序AppID
WECHAT_MINIPROGRAM_SECRET = 你的小程序Secret
```

### 2. 数据库表
执行以下SQL创建必要的数据表：
```sql
source database/machine_qrcode.sql;
```

### 3. 目录权限
确保二维码保存目录有写入权限：
```bash
chmod 755 public/static/upload/qrcode/
```

## 注意事项

1. **网络要求**: 服务器需要能够访问微信API接口
2. **存储空间**: 二维码图片会占用服务器存储空间
3. **权限管理**: 确保相关目录有适当的读写权限
4. **HTTPS建议**: 生产环境建议使用HTTPS协议

## 故障排除

### 1. 二维码生成失败
- 检查微信小程序配置是否正确
- 确认网络连接正常
- 查看服务器错误日志

### 2. 二维码无法显示
- 检查图片文件是否存在
- 确认Web服务器静态文件配置
- 验证文件路径是否正确

### 3. 批量操作无响应
- 检查是否选择了设备
- 确认JavaScript是否正常加载
- 查看浏览器控制台错误信息

## 扩展功能建议

1. **二维码样式自定义** - 支持颜色、logo等自定义
2. **批量下载功能** - 支持批量下载二维码图片
3. **使用统计** - 添加二维码扫描统计功能
4. **定时清理** - 自动清理过期的二维码文件
5. **水印功能** - 为二维码添加企业标识
